import { TFunction } from 'i18next';
import { z } from 'zod';

// Validation messages
const VALIDATION_MESSAGES = {
  vi: {
    companyNameMin: 'Tên công ty phải có ít nhất 3 ký tự',
    companyNameMax: 'Tên công ty không được vượt quá 255 ký tự',
    emailRequired: 'Email là bắt buộc',
    emailInvalid: 'Email không hợp lệ',
    passwordMin: 'Mật khẩu phải có ít nhất 8 ký tự',
    passwordMinLogin: 'Mật khẩu phải có ít nhất 6 ký tự',
    passwordUppercase: 'Mật khẩu phải chứa ít nhất một chữ hoa',
    passwordLowercase: 'Mật khẩu phải chứa ít nhất một chữ thường',
    passwordNumber: '<PERSON><PERSON><PERSON> khẩu phải chứa ít nhất một chữ số',
    passwordSpecial: 'Mật khẩu phải chứa ít nhất một ký tự đặc biệt',
    confirmPasswordRequired: 'Xác nhận mật khẩu là bắt buộc',
    passwordsMatch: 'Mật khẩu và xác nhận mật khẩu không khớp',
    phoneInvalid: 'Số điện thoại phải có 10-15 chữ số',
    fullNameMin: 'Họ và tên phải có ít nhất 2 ký tự',
  },
  en: {
    companyNameMin: 'Company name must be at least 3 characters',
    companyNameMax: 'Company name must not exceed 255 characters',
    emailRequired: 'Email is required',
    emailInvalid: 'Invalid email address',
    passwordMin: 'Password must be at least 8 characters',
    passwordMinLogin: 'Password must be at least 6 characters',
    passwordUppercase: 'Password must contain at least one uppercase letter',
    passwordLowercase: 'Password must contain at least one lowercase letter',
    passwordNumber: 'Password must contain at least one number',
    passwordSpecial: 'Password must contain at least one special character',
    confirmPasswordRequired: 'Confirm password is required',
    passwordsMatch: 'Password and confirm password do not match',
    phoneInvalid: 'Phone number must be 10-15 digits',
    fullNameMin: 'Full name must be at least 2 characters',
  },
};

// Helper function để lấy validation message theo ngôn ngữ
const getValidationMessage = (key: keyof typeof VALIDATION_MESSAGES.vi): string => {
  // Mặc định sử dụng tiếng Việt, có thể mở rộng để detect language sau
  return VALIDATION_MESSAGES.vi[key];
};

/**
 * Login form values interface
 */
export interface LoginFormValues {
  username: string;
  password: string;
  rememberMe: boolean;
  recaptchaToken?: string;
}

/**
 * Register form values interface
 */
export interface RegisterFormValues {
  fullName: string;
  email: string;
  phone: string;
  password: string;
  recaptchaToken?: string;
}

/**
 * Company register form values interface
 */
export interface CompanyRegisterFormValues {
  companyName: string;
  taxCode: string;
  companyEmail: string;
  password: string;
  confirmPassword: string;
  phoneNumber: string;
  address: string;
  recaptchaToken?: string;
}

/**
 * Forgot password form values interface
 */
export interface ForgotPasswordFormValues {
  emailOrPhone: string;
}

/**
 * Create login schema with translations
 */
export const createLoginSchema = (t: TFunction) => {
  return z.object({
    username: z
      .string()
      .min(1, t('validation.required', { field: t('auth.email') }))
      .email(t('validation.email')),
    password: z
      .string()
      .min(6, t('validation.minLength', { field: t('auth.password'), length: 6 })),
    rememberMe: z.boolean().optional(),
    recaptchaToken: z.string().optional().nullable(),
  });
};

/**
 * Create register schema with translations
 */
export const createRegisterSchema = (t: TFunction) => {
  return z.object({
    fullName: z
      .string()
      .min(2, t('validation.minLength', { field: t('auth.fullName'), length: 2 })),
    email: z
      .string()
      .min(1, t('validation.required', { field: t('auth.email') }))
      .email(t('validation.email')),
    phone: z
      .string()
      .min(10, t('validation.phone'))
      .max(11, t('validation.phone'))
      .regex(/^\d{10,11}$/, t('validation.phone')),
    password: z
      .string()
      .min(8, t('validation.minLength', { field: t('auth.password'), length: 8 }))
      .regex(/[A-Z]/, t('validation.passwordUppercase'))
      .regex(/[a-z]/, t('validation.passwordLowercase'))
      .regex(/\d/, t('validation.passwordNumber'))
      .regex(/[^\dA-Za-z]/, t('validation.passwordSpecial')),
    recaptchaToken: z.string().optional(),
  });
};

/**
 * Create forgot password schema with translations
 */
export const createForgotPasswordSchema = (t: TFunction) => {
  return z.object({
    email: z
      .string()
      .min(1, t('validation.required', { field: t('auth.email') }))
      .email(t('validation.email')),
  });
};

/**
 * Reset password form values interface
 */
export interface ResetPasswordFormValues {
  password: string;
  confirmPassword: string;
}

/**
 * Create reset password schema with translations
 */
export const createResetPasswordSchema = (t: TFunction) => {
  return z
    .object({
      password: z
        .string()
        .min(8, t('validation.minLength', { field: t('auth.password'), length: 8 }))
        .regex(/[A-Z]/, t('validation.passwordUppercase'))
        .regex(/[a-z]/, t('validation.passwordLowercase'))
        .regex(/\d/, t('validation.passwordNumber'))
        .regex(/[^\dA-Za-z]/, t('validation.passwordSpecial')),
      confirmPassword: z
        .string()
        .min(1, t('validation.required', { field: t('auth.confirmPassword') })),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: t('validation.passwordsMatch'),
      path: ['confirmPassword'],
    });
};

/**
 * Create company register schema with translations
 */
export const createCompanyRegisterSchema = (t: TFunction) => {
  return z
    .object({
      companyName: z
        .string()
        .min(3, t('validation.minLength', { field: t('auth.companyName'), length: 3 }))
        .max(255, t('validation.maxLength', { field: t('auth.companyName'), length: 255 })),
      companyEmail: z
        .string()
        .min(1, t('validation.required', { field: t('auth.email') }))
        .email(t('validation.email')),
      password: z
        .string()
        .min(8, t('validation.minLength', { field: t('auth.password'), length: 8 }))
        .regex(/[A-Z]/, t('validation.passwordUppercase'))
        .regex(/[a-z]/, t('validation.passwordLowercase'))
        .regex(/\d/, t('validation.passwordNumber'))
        .regex(/[^\dA-Za-z]/, t('validation.passwordSpecial')),
      confirmPassword: z
        .string()
        .min(1, t('validation.required', { field: t('auth.confirmPassword') })),
      phoneNumber: z
        .string()
        .min(10, t('validation.phone'))
        .max(15, t('validation.phone'))
        .regex(/^\d{10,15}$/, t('validation.phone')),
      recaptchaToken: z.string().optional(),
    })
    .refine(data => data.password === data.confirmPassword, {
      message: t('validation.passwordsMatch', 'Mật khẩu và xác nhận mật khẩu không khớp'),
      path: ['confirmPassword'],
    });
};
